import { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, mediaDevices } from 'react-native-webrtc';
import SocketService from '../../modules/chat/services/SocketService';
import CallHistoryAPI from '../../modules/call/services/CallHistoryAPI';
import store from '../../redux/store/store';
import AudioManager from '../../utils/AudioManager';

export interface CallState {
  isInCall: boolean;
  isIncoming: boolean;
  isOutgoing: boolean;
  isConnected: boolean;
  targetUserId: string | null;
  targetUserName: string | null;
  callStartTime: Date | null;
}

export interface WebRTCCallbacks {
  onLocalStream?: (stream: any) => void;
  onRemoteStream?: (stream: any) => void;
  onCallReceived?: (data: { from: string; fromName?: string, fromAvatar?: string }) => void;
  onCallAccepted?: (data: { from: string }) => void;
  onCallRejected?: (data: { from: string }) => void;
  onCallEnded?: (data: { from: string }) => void;
  onError?: (error: any) => void;
  onCallStateChanged?: (state: CallState) => void;
}

class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: any = null;
  private remoteStream: any = null;
  private isInitiator = false;
  private targetUserId: string | null = null;
  private targetUserName: string | null = null;
  private callTimeoutTimer: NodeJS.Timeout | null = null; // Timer cho timeout 60s
  private currentCallHistoryId: string | null = null; // ID của record CallHistory hiện tại
  private callAcceptedTime: Date | null = null; // Thời điểm cuộc gọi được chấp nhận
  private callState: CallState = {
    isInCall: false,
    isIncoming: false,
    isOutgoing: false,
    isConnected: false,
    targetUserId: null,
    targetUserName: null,
    callStartTime: null,
  };

  // ICE servers configuration
  private iceServers = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      // Thêm TURN servers nếu cần thiết cho production
    ]
  };

  // Callbacks
  private callbacks: WebRTCCallbacks = {};

  constructor() {
    // Không setup listeners ngay trong constructor
    // Sẽ setup khi có socket connection
  }

  // Thiết lập callbacks
  setCallbacks(callbacks: WebRTCCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Cập nhật trạng thái cuộc gọi
  private updateCallState(updates: Partial<CallState>) {
    const oldState = { ...this.callState };
    this.callState = { ...this.callState, ...updates };

    console.log('📞 WebRTC State Update:', {
      from: oldState,
      to: this.callState,
      updates
    });

    if (this.callbacks.onCallStateChanged) {
      this.callbacks.onCallStateChanged(this.callState);
    }
  }

  // Lấy trạng thái hiện tại
  getCallState(): CallState {
    return { ...this.callState };
  }

  // Thiết lập socket listeners - public method để có thể gọi từ bên ngoài
  setupSocketListeners() {
    const socket = SocketService.getSocket();
    if (!socket) {
      console.error('Socket not available for WebRTC');
      return;
    }

    console.log('📞 Setting up WebRTC socket listeners');

    // Debug: Log all socket events
    const originalOn = socket.on.bind(socket);
    socket.on = function(event: string, callback: Function) {
      if (event.includes('call') || event.includes('offer') || event.includes('answer') || event.includes('candidate')) {
        console.log('📞 Registering listener for event:', event);
      }
      return originalOn(event, (...args: any[]) => {
        if (event.includes('call') || event.includes('offer') || event.includes('answer') || event.includes('candidate')) {
          console.log('📞 Received socket event:', event, 'with data:', args);
        }
        return callback(...args);
      });
    };

    // Nhận cuộc gọi đến
    socket.on('incoming-call', (data: { from: string; socketId: string; fromName?: string; callHistoryId?: string, fromAvatar?: string }) => {
      console.log('📞 Incoming call from:', data.from);
      this.targetUserId = data.from;
      this.isInitiator = false;
      console.log('📞 Set isInitiator = false for receiver');
      this.logCurrentState('incoming-call');
      debugger
      // Lưu CallHistory ID từ người gọi để có thể update sau
      if (data.callHistoryId) {
        this.currentCallHistoryId = data.callHistoryId;
        console.log('📞 Received CallHistory ID:', data.callHistoryId);
      }

      this.updateCallState({
        isInCall: true,
        isIncoming: true,
        isOutgoing: false,
        targetUserId: data.from,
        targetUserName: data.fromName,
        callStartTime: new Date(),
      });

      if (this.callbacks.onCallReceived) {
        this.callbacks.onCallReceived({ from: data.from, fromName: data.fromName, fromAvatar: data.fromAvatar });
      }
    });

    // Cuộc gọi được chấp nhận
    socket.on('accept-call', async (data: { from: string, to?: string, callHistoryId: string }) => {
      debugger
      console.log('📞 ===== ACCEPT-CALL EVENT RECEIVED =====');
      console.log('📞 Call accepted by:', data.from);
      console.log('📞 Accept data:', data);
      console.log('📞 Current state - isInitiator:', this.isInitiator, 'targetUserId:', this.targetUserId);

      // Lấy current user để kiểm tra
      const currentUser = store.getState().customer.data;
      if (!currentUser?.Id) {
        console.log('📞 No current user found');
        return;
      }

      // Chỉ xử lý accept-call nếu current user là người được gọi đến (data.to)
      // Hoặc nếu targetUserId match với người gửi accept (data.from)
      const isCallerReceivingAccept = (data.to === currentUser.Id) || (this.targetUserId === data.from);

      console.log('📞 Accept-call check:', {
        currentUserId: currentUser.Id,
        acceptFrom: data.from,
        acceptTo: data.to,
        targetUserId: this.targetUserId,
        isCallerReceivingAccept,
        isInitiator: this.isInitiator
      });

      if (!isCallerReceivingAccept) {
        console.log('📞 Ignoring accept-call - not for this user');
        return;
      }

      // Clear timeout khi cuộc gọi được chấp nhận
      this.clearCallTimeout();

      // Lưu thời điểm chấp nhận cuộc gọi
      this.callAcceptedTime = new Date();

      // Cập nhật CallHistory cho người gọi
      if (data.callHistoryId) {
        try {
          await CallHistoryAPI.updateCallHistory({
            Id: data.callHistoryId,
            IsAccept: true,
          });
          console.log('✅ Caller CallHistory updated: IsAccept = true');
        } catch (error) {
          console.error('❌ Failed to update caller CallHistory:', error);
        }
      }

      // Cập nhật trạng thái cho người gọi - hiển thị rằng cuộc gọi đã được accept
      this.updateCallState({
        isIncoming: false, // Không còn là incoming call
        isOutgoing: true,  // Vẫn là outgoing call cho người gọi
        isConnected: false, // Chưa connected, đang setup WebRTC
        callStartTime: new Date(), // Bắt đầu tính thời gian từ khi accept
      });

      if (this.callbacks.onCallAccepted) {
        this.callbacks.onCallAccepted(data);
      }

      // Tạo offer nếu có peer connection (caller đã được xác định ở trên)
      if (this.peerConnection) {
        try {
          console.log('📞 Creating offer (caller side after accept)');
          const offer = await this.peerConnection.createOffer({});

          console.log('📞 Setting local description (offer)');
          await this.peerConnection.setLocalDescription(offer);

          console.log('📞 Sending offer to:', this.targetUserId);
          socket.emit('offer', {
            targetUserId: this.targetUserId,
            offer: offer
          });
        } catch (error) {
          console.error('📞 Error creating offer:', error);
          this.handleError(error);
        }
      } else {
        console.log('📞 No peer connection available for creating offer');
      }
    });

    // Cuộc gọi bị từ chối
    socket.on('reject-call', (data: { from: string }) => {
      console.log('Call rejected by:', data.from);

      // Clear timeout khi bị reject
      this.clearCallTimeout();

      if (this.callbacks.onCallRejected) {
        this.callbacks.onCallRejected(data);
      }

      // Cleanup ngay lập tức
      this.cleanup();
    });
    //on error
    socket.on('error', (error: any) => {
        debugger
      console.error('WebRTC Socket Error:', error);
      this.handleError(error);
    });

    // Nhận offer
    socket.on('offer', async (data: { offer: any; from: string }) => {
      console.log('📞 Received offer from:', data.from);

      if (!this.peerConnection) {
        console.log('📞 Creating peer connection for offer');
        this.createPeerConnection();
      }

      try {
        console.log('📞 Setting remote description (offer)');
        await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.offer));

        console.log('📞 Creating answer');
        const answer = await this.peerConnection!.createAnswer();

        console.log('📞 Setting local description (answer)');
        await this.peerConnection!.setLocalDescription(answer);

        console.log('📞 Sending answer to:', data.from);
        socket.emit('answer', {
          targetUserId: data.from,
          answer: answer
        });
      } catch (error) {
        console.error('📞 Error handling offer:', error);
        this.handleError(error);
      }
    });

    // Nhận answer
    socket.on('answer', async (data: { answer: any; from: string }) => {
      console.log('📞 Received answer from:', data.from);

      try {
        console.log('📞 Setting remote description (answer)');
        await this.peerConnection!.setRemoteDescription(new RTCSessionDescription(data.answer));
        console.log('📞 Answer processed successfully');
      } catch (error) {
        console.error('📞 Error handling answer:', error);
        this.handleError(error);
      }
    });

    // Nhận ICE candidate
    socket.on('candidate', async (data: { candidate: any; targetUserId: string }) => {
      console.log('📞 Received ICE candidate from:', data.targetUserId);
      console.log('📞 ICE candidate data:', data.candidate);

      if (this.peerConnection) {
        try {
          // Validate ICE candidate before adding
          const candidate = data.candidate;
          if (candidate && (candidate.sdpMLineIndex !== null || candidate.sdpMid !== null)) {
            await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
            console.log('📞 ICE candidate added successfully');
          } else {
            console.warn('📞 Invalid ICE candidate - missing sdpMLineIndex and sdpMid:', candidate);
          }
        } catch (error) {
          console.error('📞 Error adding ICE candidate:', error);
          console.error('📞 Candidate data:', data.candidate);
        }
      } else {
        console.warn('📞 No peer connection available for ICE candidate');
      }
    });

    // Cuộc gọi kết thúc
    socket.on('end-call', (data: { from: string }) => {
      console.log('Call ended by:', data.from);

      if (this.callbacks.onCallEnded) {
        this.callbacks.onCallEnded(data);
      }
      this.cleanup();
    });
  }

  // Tạo peer connection
  private createPeerConnection() {
    console.log('Creating peer connection with ICE servers:', this.iceServers);
    this.peerConnection = new RTCPeerConnection(this.iceServers);

    // Xử lý ICE candidates
    (this.peerConnection as any).onicecandidate = (event: any) => {
      if (event.candidate) {
        console.log('📞 Sending ICE candidate to:', this.targetUserId);
        console.log('📞 ICE candidate details:', {
          sdpMLineIndex: event.candidate.sdpMLineIndex,
          sdpMid: event.candidate.sdpMid,
          candidate: event.candidate.candidate
        });

        const socket = SocketService.getSocket();
        if (socket) {
          socket.emit('candidate', {
            candidate: event.candidate,
            targetUserId: this.targetUserId
          });
        }
      } else {
        console.log('📞 ICE gathering completed');
      }
    };

    // Xử lý remote stream - sử dụng ontrack thay vì onaddstream
    (this.peerConnection as any).ontrack = (event: any) => {
      console.log('Received remote track:', event.track.kind);
      if (event.streams && event.streams[0]) {
        this.remoteStream = event.streams[0];

        // Log remote stream details
        const audioTracks = event.streams[0].getAudioTracks();
        console.log('Remote audio tracks:', audioTracks.length);
        audioTracks.forEach((track: any, index: number) => {
          console.log(`Remote audio track ${index}:`, {
            kind: track.kind,
            enabled: track.enabled,
            readyState: track.readyState,
            label: track.label
          });
        });

        if (this.callbacks.onRemoteStream) {
          this.callbacks.onRemoteStream(event.streams[0]);
        }
      }
    };

    // Xử lý connection state changes
    (this.peerConnection as any).onconnectionstatechange = () => {
      const state = this.peerConnection?.connectionState;
      console.log('📞 Connection state changed to:', state);

      if (state === 'connected') {
        console.log('📞 ✅ WebRTC connection established successfully');
        this.updateCallState({ isConnected: true });
      } else if (state === 'disconnected' || state === 'failed') {
        console.log('📞 ❌ WebRTC connection lost:', state);
        this.cleanup();
      } else if (state === 'connecting') {
        console.log('📞 🔄 WebRTC connecting...');
      }
    };
  }

  // Lấy local media stream (chỉ audio)
  private async getLocalStream(): Promise<any> {
    try {
      const constraints = {
        audio: true, // react-native-webrtc sử dụng boolean cho audio
        video: false // Chỉ audio call
      };

      console.log('Requesting media permissions...');
      this.localStream = await mediaDevices.getUserMedia(constraints);
      console.log('Local stream obtained successfully');

      // Setup audio session
      await AudioManager.setupAudioSession();

      // Log stream details
      if (this.localStream) {
        const audioTracks = this.localStream.getAudioTracks();
        console.log('Audio tracks:', audioTracks.length);
        audioTracks.forEach((track: any, index: number) => {
          console.log(`Audio track ${index}:`, {
            kind: track.kind,
            enabled: track.enabled,
            readyState: track.readyState,
            label: track.label
          });
        });
      }

      if (this.callbacks.onLocalStream) {
        this.callbacks.onLocalStream(this.localStream);
      }

      return this.localStream;
    } catch (error: any) {
      console.error('Error getting local stream:', error);
      // Thêm thông tin chi tiết về lỗi
      if (error?.name === 'NotAllowedError') {
        throw new Error('Microphone permission denied');
      } else if (error?.name === 'NotFoundError') {
        throw new Error('No microphone found');
      } else {
        throw new Error('Failed to access microphone: ' + (error?.message || 'Unknown error'));
      }
    }
  }

  // Bắt đầu cuộc gọi
  async startCall(targetUserId: string, targetUserName?: string, targetUserAvatar?: string): Promise<void> {
    try {
      console.log('📞 Starting call to:', targetUserId);
      this.targetUserId = targetUserId;
      this.targetUserName = targetUserName || null;
      this.isInitiator = true;
      console.log('📞 Set isInitiator = true for caller');
      this.logCurrentState('startCall');

      // Lấy thông tin user hiện tại
      const currentUser = store.getState().customer.data;
      if (!currentUser?.Id) {
        throw new Error('Current user not found');
      }

      // Tạo CallHistory record
      const callHistory = await CallHistoryAPI.createCallHistory({
        Name: this.targetUserName || 'Unknown',
        Receiver: targetUserId,
        CustomerId: currentUser.Id,
      });
      debugger
      if (callHistory) {
        this.currentCallHistoryId = callHistory.Id;
        console.log('✅ CallHistory created:', callHistory.Id);
      } else {
        console.error('❌ Failed to create CallHistory');
      }

      this.updateCallState({
        isInCall: true,
        isOutgoing: true,
        isIncoming: false,
        targetUserId,
        targetUserName: this.targetUserName,
        callStartTime: new Date(),
      });

      // Tạo peer connection
      this.createPeerConnection();

      // Lấy local stream
      await this.getLocalStream();

      // Thêm local stream vào peer connection
      if (this.localStream && this.peerConnection) {
        console.log('Adding local stream tracks to peer connection');
        // Sử dụng addTrack thay vì addStream cho react-native-webrtc mới
        this.localStream.getTracks().forEach((track: any) => {
          console.log('Adding track:', track.kind);
          this.peerConnection!.addTrack(track, this.localStream);
        });
      }

      // Gửi call request
      const socket = SocketService.getSocket();
      debugger
      if (socket) {
        socket.emit('call-user', {
          targetUserId: targetUserId,
          fromName: this.targetUserName,
          callHistoryId: this.currentCallHistoryId,
          targetUserAvatar: targetUserAvatar,
        });
      }

      // Thiết lập timeout 60 giây
      this.startCallTimeout();

      console.log('Call initiated to:', targetUserId);
    } catch (error) {
      console.error('Error starting call:', error);
      this.handleError(error);
    }
  }

  // Chấp nhận cuộc gọi
  async acceptCall(): Promise<void> {
    try {
      console.log('📞 acceptCall() called - isInitiator:', this.isInitiator, 'targetUserId:', this.targetUserId);
      this.logCurrentState('acceptCall-start');

      // Clear timeout khi cuộc gọi được chấp nhận
      this.clearCallTimeout();

      // Lưu thời điểm chấp nhận cuộc gọi
      this.callAcceptedTime = new Date();

      // Cập nhật CallHistory - người nhận accept cuộc gọi
      if (this.currentCallHistoryId) {
        try {
          await CallHistoryAPI.updateCallHistory({
            Id: this.currentCallHistoryId,
            IsAccept: true,
          });
          console.log('✅ Receiver updated CallHistory: IsAccept = true');
        } catch (error) {
          console.error('❌ Failed to update CallHistory on accept:', error);
        }
      }

      // Tạo peer connection nếu chưa có
      if (!this.peerConnection) {
        this.createPeerConnection();
      }

      // Lấy local stream
      await this.getLocalStream();

      // Thêm local stream vào peer connection
      if (this.localStream && this.peerConnection) {
        console.log('📞 Adding local stream tracks to peer connection');
        // Sử dụng addTrack thay vì addStream cho react-native-webrtc mới
        this.localStream.getTracks().forEach((track: any) => {
          console.log('📞 Adding track:', track.kind, track.enabled);
          this.peerConnection!.addTrack(track, this.localStream);
        });
      }

      console.log('📞 WebRTC: Updating state after accept call');
      this.updateCallState({
        isIncoming: false, // Set to false khi accept để show in-call controls
        isConnected: false, // Chưa connected, đợi WebRTC handshake hoàn tất
        callStartTime: new Date(),
      });
      console.log('📞 WebRTC: State updated, new state:', this.callState);

      // Lấy thông tin user hiện tại để gửi đúng ID
      const currentUser = store.getState().customer.data;
      if (!currentUser?.Id) {
        throw new Error('Current user not found');
      }

      console.log('📞 Current user ID:', currentUser.Id, 'Target user ID:', this.targetUserId);

      // Delay một chút để đảm bảo peer connection đã sẵn sàng
      await new Promise(resolve => setTimeout(resolve, 100));

      // Gửi accept call với callHistoryId
      const socket = SocketService.getSocket();
      if (socket) {
        const acceptData = {
          from: currentUser.Id, // ID của người nhận (người accept)
          to: this.targetUserId, // ID của người gọi (người cần nhận accept signal)
          callHistoryId: this.currentCallHistoryId
        };
        debugger
        console.log('📞 ===== EMITTING ACCEPT-CALL =====');
        console.log('📞 Sending accept-call signal:', acceptData);
        console.log('📞 Socket connected:', socket.connected);

        socket.emit('accept-call', acceptData);

        console.log('📞 Accept-call event emitted successfully');
      } else {
        console.error('📞 No socket available to emit accept-call');
      }

      console.log('Call accepted');
    } catch (error) {
      console.error('Error accepting call:', error);
      this.handleError(error);
    }
  }

  // Từ chối cuộc gọi
  rejectCall(): void {
    const socket = SocketService.getSocket();
    if (socket) {
      socket.emit('reject-call', {
        from: this.targetUserId
      });
    }
    this.endCallWithReason('rejected');
  }

  // Kết thúc cuộc gọi
  endCall(): void {
    this.endCallWithReason('completed');
  }

  // Xử lý lỗi
  private handleError(error: any): void {
    console.error('WebRTC Error:', error);
    if (this.callbacks.onError) {
      this.callbacks.onError(error);
    }
    this.cleanup();
  }

  // Bắt đầu timeout timer cho cuộc gọi
  private startCallTimeout(): void {
    // Clear timeout cũ nếu có
    this.clearCallTimeout();

    // Thiết lập timeout 60 giây
    this.callTimeoutTimer = setTimeout(() => {
      console.log('⏰ Call timeout after 60 seconds');

      // Gọi callback timeout nếu có
      if (this.callbacks.onError) {
        this.callbacks.onError(new Error('Call timeout - No response after 60 seconds'));
      }

      // Kết thúc cuộc gọi
      this.endCallWithReason('timeout');
    }, 600000); // 60 giây

    console.log('⏰ Call timeout timer started (60s)');
  }

  // Xóa timeout timer
  private clearCallTimeout(): void {
    if (this.callTimeoutTimer) {
      clearTimeout(this.callTimeoutTimer);
      this.callTimeoutTimer = null;
      console.log('⏰ Call timeout timer cleared');
    }
  }

  // Kết thúc cuộc gọi với lý do cụ thể
  private async endCallWithReason(reason: 'timeout' | 'rejected' | 'completed' | 'cancelled'): Promise<void> {
    console.log('📞 Ending call with reason:', reason);

    // Clear timeout
    this.clearCallTimeout();

    // Cập nhật CallHistory với thời gian cuộc gọi
    if (this.currentCallHistoryId && this.callAcceptedTime) {
      try {
        const callDuration = Math.floor((new Date().getTime() - this.callAcceptedTime.getTime()) / 1000);
        await CallHistoryAPI.updateCallHistory({
          Id: this.currentCallHistoryId,
          Time: callDuration,
        });
        console.log(`✅ CallHistory updated: Time = ${callDuration}s, EndReason = ${reason}`);
      } catch (error) {
        console.error('❌ Failed to update CallHistory:', error);
      }
    }

    // Gửi end call signal nếu cần
    if (reason !== 'timeout') {
      const socket = SocketService.getSocket();
      if (socket) {
        socket.emit('end-call', {
          targetUserId: this.targetUserId
        });
      }
    }

    // Cleanup resources
    this.cleanup();
  }

  // Dọn dẹp resources
  private cleanup(): void {
    console.log('📞 Cleaning up WebRTC resources');

    // Clear timeout timer
    this.clearCallTimeout();

    // Cleanup audio session
    AudioManager.cleanupAudioSession();

    // Dừng local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach((track: any) => {
        track.stop();
      });
      this.localStream = null;
    }

    // Dừng remote stream
    if (this.remoteStream) {
      this.remoteStream.getTracks().forEach((track: any) => {
        track.stop();
      });
      this.remoteStream = null;
    }

    // Đóng peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset state
    this.resetCallState();
  }

  // Reset call state
  private resetCallState(): void {
    console.log('📞 Resetting call state - isInitiator was:', this.isInitiator);
    this.targetUserId = null;
    this.targetUserName = null;
    this.isInitiator = false;
    this.currentCallHistoryId = null;
    this.callAcceptedTime = null;
    console.log('📞 isInitiator reset to false');

    this.updateCallState({
      isInCall: false,
      isIncoming: false,
      isOutgoing: false,
      isConnected: false,
      targetUserId: null,
      targetUserName: null,
      callStartTime: null,
    });
  }

  // Kiểm tra xem có đang trong cuộc gọi không
  isInCall(): boolean {
    return this.callState.isInCall;
  }

  // Lấy thông tin người đang gọi
  getCurrentCallInfo(): { targetUserId: string | null; targetUserName: string | null } {
    return {
      targetUserId: this.targetUserId,
      targetUserName: this.targetUserName,
    };
  }

  // Force reset for debugging
  forceReset(): void {
    console.log('📞 Force resetting WebRTC service');
    this.cleanup();
  }

  // Get debug info
  getDebugInfo(): any {
    const currentUser = store.getState().customer.data;
    return {
      currentUserId: currentUser?.Id,
      isInitiator: this.isInitiator,
      targetUserId: this.targetUserId,
      targetUserName: this.targetUserName,
      callState: this.callState,
      hasPeerConnection: !!this.peerConnection,
      hasLocalStream: !!this.localStream,
      hasRemoteStream: !!this.remoteStream,
      timestamp: new Date().toISOString(),
    };
  }

  // Log current state for debugging
  logCurrentState(context: string): void {
    console.log(`📞 [${context}] WebRTC State:`, this.getDebugInfo());
  }

  // Test socket connection
  testSocket(): void {
    const socket = SocketService.getSocket();
    if (socket) {
      console.log('📞 Testing socket connection...');
      console.log('📞 Socket connected:', socket.connected);
      console.log('📞 Socket ID:', socket.id);

      // Test emit/receive
      socket.emit('test-call-feature', { test: true, timestamp: Date.now() });

      // Listen for response
      socket.once('test-call-response', (data: any) => {
        console.log('📞 Test response received:', data);
      });
    } else {
      console.error('📞 No socket available for testing');
    }
  }
}

// Make WebRTCService available globally for debugging
if (typeof global !== 'undefined') {
  (global as any).WebRTCService = WebRTCService;
}

export default new WebRTCService();