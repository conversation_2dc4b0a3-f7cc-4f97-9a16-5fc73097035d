# Debug ICE Candidate Issue

## Lỗi hiện tại
```
Error adding ICE candidate: TypeError: `sdpMLineIndex` and `sdpMid` must not be both null
```

## Nguyên nhân
ICE candidate data không đúng format hoặc bị corrupt trong quá trình truyền qua socket.

## Fixes Applied

### 1. Validate ICE Candidate Before Adding
```typescript
if (candidate && (candidate.sdpMLineIndex !== null || candidate.sdpMid !== null)) {
  await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
} else {
  console.warn('Invalid ICE candidate');
}
```

### 2. Enhanced ICE Candidate Logging
- Log ICE candidate details khi gửi
- Log ICE candidate data khi nhận
- Log validation results

### 3. Better Connection State Handling
- Separate handling cho connection state và ICE state
- Don't cleanup immediately on disconnect
- Only cleanup on failed state

## Test Steps

### 1. <PERSON><PERSON><PERSON> tra ICE Candidate Format
Logs cần thấy khi gửi:
```
📞 Sending ICE candidate to: [targetUserId]
📞 ICE candidate details: {
  sdpMLineIndex: 0,
  sdpMid: "0", 
  candidate: "candidate:..."
}
```

### 2. Kiểm tra ICE Candidate Reception
Logs cần thấy khi nhận:
```
📞 Received ICE candidate from: [userId]
📞 ICE candidate data: {sdpMLineIndex: 0, sdpMid: "0", candidate: "..."}
📞 ICE candidate added successfully
```

### 3. Kiểm tra Connection States
```
📞 Connection state changed to: connecting
📞 ICE connection state changed to: checking
📞 ICE connection state changed to: connected
📞 Connection state changed to: connected
📞 ✅ WebRTC connection established successfully
```

## Common ICE Issues

### 1. Network/Firewall Issues
- ICE candidates không thể reach nhau
- Cần TURN server cho production

### 2. Invalid Candidate Data
- Server có thể modify candidate data
- JSON serialization issues

### 3. Timing Issues
- ICE candidates arrive before remote description set
- Need to queue candidates

## Debug Commands

```bash
# Filter ICE-related logs
npx react-native log-android | grep -E "(📞|ICE|candidate|Connection)"
```

## Expected Flow

### Successful ICE Exchange:
1. **Caller creates offer**
   ```
   📞 Creating offer (caller side after accept)
   📞 Setting local description (offer)
   📞 Sending offer to: [receiver]
   ```

2. **ICE candidates start flowing**
   ```
   📞 Sending ICE candidate to: [receiver]
   📞 ICE candidate details: {sdpMLineIndex: 0, ...}
   ```

3. **Receiver gets offer and creates answer**
   ```
   📞 Received offer from: [caller]
   📞 Setting remote description (offer)
   📞 Creating answer
   📞 Setting local description (answer)
   ```

4. **ICE candidates exchanged both ways**
   ```
   📞 Received ICE candidate from: [caller]
   📞 ICE candidate added successfully
   ```

5. **Connection established**
   ```
   📞 ICE connection state changed to: connected
   📞 Connection state changed to: connected
   📞 ✅ WebRTC connection established successfully
   ```

## Troubleshooting

### If ICE candidates are invalid:
1. Check server logs - is server modifying candidate data?
2. Check network setup - are candidates reachable?
3. May need TURN server for production

### If connection fails:
1. Check firewall settings
2. Test on same network first
3. Check STUN server accessibility

### If no audio after connection:
1. Check audio tracks in streams
2. Check audio permissions
3. Check audio routing (speaker/earpiece)
